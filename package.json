{"name": "saasrock-enterprise", "version": "1.4.3", "private": true, "sideEffects": false, "type": "module", "prisma": {"seed": "tsx prisma/seed.ts"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,css,md,json}": "prettier --write", "**/*.{js,jsx,ts,tsx}": "eslint --fix", "**/*.{ts,tsx}": "bash -c \"tsc -p tsconfig.json --pretty\""}, "scripts": {"dev": "remix vite:dev", "build": "prisma generate && remix vite:build && npm run post-build", "post-build": "npx puppeteer browsers install", "lint": "eslint --ext .tsx,.ts .", "typecheck": "tsc -b", "test": "vitest app/", "prettier": "prettier --write '**/*.{ts,tsx}'", "start": "remix-serve ./build/server/index.js", "pre-commit": "lint-staged --allow-empty --concurrent false", "prepare": "husky install"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/lib-storage": "^3.750.0", "@dinero.js/currencies": "^2.0.0-alpha.1", "@epic-web/cachified": "^4.0.0", "@headlessui/react": "^2.1.7", "@headlessui/tailwindcss": "^0.2.0", "@heroicons/react": "^2.1.5", "@monaco-editor/react": "^4.4.5", "@novu/node": "^0.24.2", "@novu/react": "^2.1.0", "@popperjs/core": "^2.11.8", "@prisma/client": "^5.21.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@remix-run/node": "2.15.3", "@remix-run/react": "^2.12.0", "@remix-run/serve": "^2.12.0", "@remix-run/v1-route-convention": "^0.1.4", "@shadcn/ui": "^0.0.4", "@sindresorhus/slugify": "^2.2.1", "@stripe/stripe-js": "^1.35.0", "@supabase/supabase-js": "^2.26.0", "@tiptap/core": "^2.1.13", "@tiptap/extension-color": "^2.1.13", "@tiptap/extension-horizontal-rule": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-placeholder": "^2.0.3", "@tiptap/extension-text-style": "^2.1.13", "@tiptap/extension-underline": "^2.1.13", "@tiptap/pm": "^2.1.13", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/suggestion": "^2.1.13", "@tremor/react": "^3.17.2", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.4.0", "@vercel/remix": "^2.9.2-patch.2", "ajv": "^8.12.0", "bcryptjs": "^2.4.3", "browser-image-compression": "^1.0.17", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "clsx": "^1.2.1", "cmdk": "^0.2.1", "company-email-validator": "^1.0.7", "crypto-js": "^4.2.0", "dagre": "^0.8.5", "date-fns": "^3.3.1", "decimal.js": "^10.4.3", "embla-carousel-react": "^8.0.0", "eventsource-parser": "^1.0.0", "framer-motion": "^11.11.15", "handlebars": "^4.7.8", "highlight.js": "^11.9.0", "html-to-text": "^9.0.5", "i18next": "^23.16.3", "i18next-browser-languagedetector": "^7.1.0", "i18next-fs-backend": "^2.2.0", "i18next-http-backend": "^2.2.2", "is-ip": "^3.1.0", "isbot": "^3.7.0", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.0", "kbar": "^0.1.0-beta.45", "lru-cache": "^10.1.0", "lucide-react": "^0.244.0", "mailchecker": "^6.0.1", "marked": "^4.0.14", "moment": "^2.29.1", "nanoid": "^3.3.6", "next-themes": "^0.2.1", "numeral": "^2.0.6", "openai": "^4.47.2", "platform": "^1.3.6", "postmark": "^3.11.0", "puppeteer": "^23.11.1", "react": "^18.3.0-canary-593ecee66-20231114", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.1.0", "react-countup": "^6.5.3", "react-day-picker": "^8.10.0", "react-dom": "^18.3.0-canary-593ecee66-20231114", "react-google-recaptcha": "^2.1.0", "react-google-recaptcha-ultimate": "^1.2.2", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-i18next": "^15.1.0", "react-international-phone": "^4.5.0", "react-json-tree": "^0.17.0", "react-pdftotext": "^1.3.4", "react-phone-number-input": "^3.4.11", "react-popover": "^0.5.10", "react-sortablejs": "^6.1.4", "reactflow": "^11.10.1", "recharts": "^2.15.0", "rehype-highlight": "^5.0.2", "remix-auth": "^3.6.0", "remix-auth-form": "^1.4.0", "remix-auth-google": "^1.3.0", "remix-i18next": "^6.4.1", "remix-typedjson": "^0.4.1", "resend": "^2.0.0", "sonner": "^1.4.2", "sortablejs": "^1.15.0", "spin-delay": "^1.1.0", "stripe": "^13.11.0", "swagger-ui-dist": "^4.19.0", "swr": "^2.2.0", "tailwind-merge": "^2.2.0", "tiny-invariant": "^1.3.1", "tippy.js": "^6.3.7", "use-debounce": "^9.0.3", "uuid": "^9.0.1"}, "devDependencies": {"@remix-run/dev": "2.15.3", "@remix-run/eslint-config": "^2.12.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "@testing-library/cypress": "^8.0.2", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^14.1.2", "@types/adm-zip": "^0.5.5", "@types/bcryptjs": "^2.4.2", "@types/crypto-js": "^4.2.1", "@types/dagre": "^0.7.52", "@types/html-to-text": "^9.0.1", "@types/json2csv": "^5.0.3", "@types/jsonwebtoken": "^9.0.6", "@types/marked": "^4.0.3", "@types/numeral": "^2.0.2", "@types/platform": "^1.3.4", "@types/postmark": "^2.0.3", "@types/react": "^18.3.2", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-command-palette": "^0.18.0", "@types/react-datepicker": "^4.3.4", "@types/react-dom": "^18.3.0", "@types/react-google-recaptcha": "^2.1.5", "@types/react-table": "^7.7.11", "@types/uuid": "^8.3.4", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "eslint": "^8.54.0", "husky": "^8.0.0", "jsdom": "^23.0.1", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.1.13", "prisma": "^5.21.1", "remix-flat-routes": "^0.6.2", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "terser": "^5.39.0", "tsx": "^4.6.2", "typescript": "^5.3.2", "vite": "^5.4.16", "vite-plugin-compression": "^0.5.1", "vite-tsconfig-paths": "^4.2.2", "vitest": "^0.29.8"}}