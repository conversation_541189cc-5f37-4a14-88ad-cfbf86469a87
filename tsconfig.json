{"include": ["env.d.ts", "**/*.ts", "**/*.tsx"], "compilerOptions": {"skipLibCheck": true, "lib": ["DOM", "DOM.Iterable", "ES2022"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "target": "ES2022", "module": "ES2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "strict": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"], "~/public/*": ["./public/*"]}, "noEmit": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "allowImportingTsExtensions": true}}