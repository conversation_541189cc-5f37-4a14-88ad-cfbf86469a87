import { Colors } from "~/application/enums/shared/Colors";

function get100(itemColor: Colors): string {
  switch (itemColor) {
    case Colors.UNDEFINED:
      return "border-gray-100";
    case Colors.SLATE:
      return "border-slate-100";
    case Colors.GRAY:
      return "border-gray-100";
    case Colors.NEUTRAL:
      return "border-neutral-100";
    case Colors.STONE:
      return "border-stone-100";
    case Colors.RED:
      return "border-red-100";
    case Colors.ORANGE:
      return "border-orange-100";
    case Colors.AMBER:
      return "border-amber-100";
    case Colors.YELLOW:
      return "border-yellow-100";
    case Colors.LIME:
      return "border-lime-100";
    case Colors.GREEN:
      return "border-green-100";
    case Colors.EMERALD:
      return "border-emerald-100";
    case Colors.TEAL:
      return "border-teal-100";
    case Colors.CYAN:
      return "border-cyan-100";
    case Colors.SKY:
      return "border-sky-100";
    case Colors.BLUE:
      return "border-blue-100";
    case Colors.INDIGO:
      return "border-indigo-100";
    case Colors.VIOLET:
      return "border-violet-100";
    case Colors.PURPLE:
      return "border-purple-100";
    case Colors.FUCHSIA:
      return "border-fuchsia-100";
    case Colors.PINK:
      return "border-pink-100";
    case Colors.ROSE:
      return "border-rose-100";
  }
}

function get200(itemColor: Colors): string {
  switch (itemColor) {
    case Colors.UNDEFINED:
      return "border-gray-200";
    case Colors.SLATE:
      return "border-slate-200";
    case Colors.GRAY:
      return "border-gray-200";
    case Colors.NEUTRAL:
      return "border-neutral-200";
    case Colors.STONE:
      return "border-stone-200";
    case Colors.RED:
      return "border-red-200";
    case Colors.ORANGE:
      return "border-orange-200";
    case Colors.AMBER:
      return "border-amber-200";
    case Colors.YELLOW:
      return "border-yellow-200";
    case Colors.LIME:
      return "border-lime-200";
    case Colors.GREEN:
      return "border-green-200";
    case Colors.EMERALD:
      return "border-emerald-200";
    case Colors.TEAL:
      return "border-teal-200";
    case Colors.CYAN:
      return "border-cyan-200";
    case Colors.SKY:
      return "border-sky-200";
    case Colors.BLUE:
      return "border-blue-200";
    case Colors.INDIGO:
      return "border-indigo-200";
    case Colors.VIOLET:
      return "border-violet-200";
    case Colors.PURPLE:
      return "border-purple-200";
    case Colors.FUCHSIA:
      return "border-fuchsia-200";
    case Colors.PINK:
      return "border-pink-200";
    case Colors.ROSE:
      return "border-rose-200";
  }
}

function get300(itemColor: Colors): string {
  switch (itemColor) {
    case Colors.UNDEFINED:
      return "border-gray-300";
    case Colors.SLATE:
      return "border-slate-300";
    case Colors.GRAY:
      return "border-gray-300";
    case Colors.NEUTRAL:
      return "border-neutral-300";
    case Colors.STONE:
      return "border-stone-300";
    case Colors.RED:
      return "border-red-300";
    case Colors.ORANGE:
      return "border-orange-300";
    case Colors.AMBER:
      return "border-amber-300";
    case Colors.YELLOW:
      return "border-yellow-300";
    case Colors.LIME:
      return "border-lime-300";
    case Colors.GREEN:
      return "border-green-300";
    case Colors.EMERALD:
      return "border-emerald-300";
    case Colors.TEAL:
      return "border-teal-300";
    case Colors.CYAN:
      return "border-cyan-300";
    case Colors.SKY:
      return "border-sky-300";
    case Colors.BLUE:
      return "border-blue-300";
    case Colors.INDIGO:
      return "border-indigo-300";
    case Colors.VIOLET:
      return "border-violet-300";
    case Colors.PURPLE:
      return "border-purple-300";
    case Colors.FUCHSIA:
      return "border-fuchsia-300";
    case Colors.PINK:
      return "border-pink-300";
    case Colors.ROSE:
      return "border-rose-300";
  }
}

function get900(itemColor: Colors): string {
  switch (itemColor) {
    case Colors.UNDEFINED:
      return "border-gray-900";
    case Colors.SLATE:
      return "border-slate-900";
    case Colors.GRAY:
      return "border-gray-900";
    case Colors.NEUTRAL:
      return "border-neutral-900";
    case Colors.STONE:
      return "border-stone-900";
    case Colors.RED:
      return "border-red-900";
    case Colors.ORANGE:
      return "border-orange-900";
    case Colors.AMBER:
      return "border-amber-900";
    case Colors.YELLOW:
      return "border-yellow-900";
    case Colors.LIME:
      return "border-lime-900";
    case Colors.GREEN:
      return "border-green-900";
    case Colors.EMERALD:
      return "border-emerald-900";
    case Colors.TEAL:
      return "border-teal-900";
    case Colors.CYAN:
      return "border-cyan-900";
    case Colors.SKY:
      return "border-sky-900";
    case Colors.BLUE:
      return "border-blue-900";
    case Colors.INDIGO:
      return "border-indigo-900";
    case Colors.VIOLET:
      return "border-violet-900";
    case Colors.PURPLE:
      return "border-purple-900";
    case Colors.FUCHSIA:
      return "border-fuchsia-900";
    case Colors.PINK:
      return "border-pink-900";
    case Colors.ROSE:
      return "border-rose-900";
  }
}

function getLeft400(itemColor: Colors): string {
  switch (itemColor) {
    case Colors.UNDEFINED:
      return "border-l-gray-400";
    case Colors.SLATE:
      return "border-l-slate-400";
    case Colors.GRAY:
      return "border-l-gray-400";
    case Colors.NEUTRAL:
      return "border-l-neutral-400";
    case Colors.STONE:
      return "border-l-stone-400";
    case Colors.RED:
      return "border-l-red-400";
    case Colors.ORANGE:
      return "border-l-orange-400";
    case Colors.AMBER:
      return "border-l-amber-400";
    case Colors.YELLOW:
      return "border-l-yellow-400";
    case Colors.LIME:
      return "border-l-lime-400";
    case Colors.GREEN:
      return "border-l-green-400";
    case Colors.EMERALD:
      return "border-l-emerald-400";
    case Colors.TEAL:
      return "border-l-teal-400";
    case Colors.CYAN:
      return "border-l-cyan-400";
    case Colors.SKY:
      return "border-l-sky-400";
    case Colors.BLUE:
      return "border-l-blue-400";
    case Colors.INDIGO:
      return "border-l-indigo-400";
    case Colors.VIOLET:
      return "border-l-violet-400";
    case Colors.PURPLE:
      return "border-l-purple-400";
    case Colors.FUCHSIA:
      return "border-l-fuchsia-400";
    case Colors.PINK:
      return "border-l-pink-400";
    case Colors.ROSE:
      return "border-l-rose-400";
  }
}

export default {
  get100,
  get200,
  get300,
  get900,
  getLeft400,
};
