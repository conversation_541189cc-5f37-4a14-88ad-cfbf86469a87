trigger:
  - none
 
jobs:
  - job: linkworks_ssh_dev_deployment
    workspace:
      clean: all
    pool:
      vmImage: ubuntu-latest
    steps:
      - task: CopyFiles@2
        displayName: Copy artifacts to publish
        inputs:
          SourceFolder: $(Build.SourcesDirectory)
          Contents: |
            **/?(app|prisma|public|signatures|workflows)/**
            **.json
            **.ts
            **.tsx
            **.js
            *.toml
            *.sh
          TargetFolder: $(Build.SourcesDirectory)/pipeline-publish/applicationcode
      - task: DownloadSecureFile@1
        inputs:
          secureFile: "linkworks-saasrock-dev.env"
        displayName: download .env file from library
      - script: |
          mv $(Agent.TempDirectory)/linkworks-saasrock-dev.env $(Build.SourcesDirectory)/pipeline-publish/applicationcode/.env
        displayName: move .env file to that directory that are zip and publish
      - task: ArchiveFiles@2
        inputs:
          rootFolderOrFile: "$(Build.SourcesDirectory)/pipeline-publish/applicationcode"
          includeRootFolder: false
          archiveType: "zip"
          archiveFile: $(Build.SourcesDirectory)/build.zip
          replaceExistingArchive: true
        displayName: "Archieving the source code as zip"
        
      - task: CopyFilesOverSSH@0
        inputs:
          sshEndpoint: "linkworks-dev-ssh-cicd"
          sourceFolder: $(Build.SourcesDirectory)
          contents: build.zip
          targetFolder: /tmp
          overwrite: true
        displayName: "Copy files to AWS instance using ssh"
 
      - task: SSH@0
        inputs:
          sshEndpoint: "linkworks-dev-ssh-cicd"
          runOptions: "commands"
          commands: |
            sudo systemctl stop pm2-root
            cd /home/<USER>/saasrock && sudo rm -rf apps-bkp.zip && sudo zip -r apps-bkp.zip apps && sudo rm -rf apps && sudo mkdir apps
            sudo cp /tmp/build.zip /home/<USER>/saasrock
            cd /home/<USER>/saasrock/apps && sudo unzip /home/<USER>/saasrock/build.zip && sudo npm i 2>&1 && sudo npm run build 2>&1 && sudo npx prisma db push 2>&1
            sudo systemctl restart pm2-root
            sudo systemctl restart nginx
        displayName: "Application Deployment using ssh"